:root {
  /* Color Palette */
  --color-primary: #007bff; /* Professional Blue */
  --color-primary-dark: #0056b3; /* Darker Blue for hover */
  --color-secondary: #6c757d; /* Secondary Gray */
  --color-success: #28a745; /* Green for success */
  --color-warning: #ffc107; /* Yellow for warning */
  --color-danger: #dc3545; /* Red for danger/error */
  --color-info: #17a2b8; /* Cyan for informational */

  /* Neutral Palette */
  --color-white: #ffffff;
  --color-light-gray: #f8f9fa; /* Very light background */
  --color-mid-light-gray: #e9ecef; /* Borders, separators */
  --color-gray: #ced4da; /* Inputs, borders */
  --color-dark-gray: #6c757d; /* Secondary text */
  --color-very-dark-gray: #343a40; /* Body text */

  /* Background */
  --color-background: var(--color-light-gray);

  /* Typography */
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif; /* Using Inter as an example, fallback to system fonts */
  line-height: 1.6;
  font-weight: 400;
  color: var(--color-very-dark-gray); /* Default text color */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

   /* Surgery Type Colors */
  --color-surgery-cabg: #007bff; /* Blue - Cardiac */
  --color-surgery-knee: #28a745; /* Green - Orthopedic */
  --color-surgery-appen: #ffc107; /* Yellow - General */
  --color-surgery-herni: #6c757d; /* Gray - General */
  --color-surgery-catar: #17a2b8; /* Cyan - Ophthalmology */
  --color-surgery-hipre: #20c997; /* Teal - Orthopedic */

  --color-sdst-segment: rgba(255, 255, 255, 0.3); /* Semi-transparent overlay for SDST in blocks */
  --color-sdst-border: rgba(0, 0, 0, 0.2); /* Border around SDST segment */

  /* Additional colors for UI states */
  --color-background-soft: #f1f5f9; /* Slightly darker than background for panels */
  --color-background-mute: #e2e8f0; /* Even darker for active elements */
  --color-background-hover: rgba(0, 0, 0, 0.02); /* Subtle hover effect */
  --color-background-active: rgba(0, 0, 0, 0.05); /* Slightly stronger for active state */

  /* Text colors */
  --color-text: var(--color-very-dark-gray);
  --color-text-secondary: var(--color-dark-gray);
  --color-text-inverted: var(--color-white);

  /* Border colors */
  --color-border: var(--color-mid-light-gray);
  --color-border-soft: var(--color-light-gray);

  /* Accent color for highlights */
  --color-accent: #f59e0b; /* Amber for highlights and current time indicator */

  /* Error color with RGB components for alpha operations */
  --color-error: #dc3545;
  --color-error-rgb: 220, 53, 69;

  /* Primary color with RGB components for alpha operations */
  --color-primary-rgb: 0, 123, 255;

  /* Focus outline color */
  --color-focus: var(--color-primary);


  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Typography */
  --font-size-xs: 0.75rem; /* ~12px */
  --font-size-sm: 0.875rem; /* ~14px */
  --font-size-base: 1rem; /* ~16px */
  --font-size-lg: 1.125rem; /* ~18px */
  --font-size-xl: 1.25rem; /* ~20px */
  --font-size-xxl: 1.5rem; /* ~24px */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Border Radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* Z-index */
  --z-index-tooltip: 100;
  --z-index-modal: 1000;
  --z-index-mobile-nav: 1100;

  /* Layout */
  --header-height: 60px;
  --footer-height: 0px;
  --sidebar-width: 240px;
  --sidebar-width-collapsed: 60px;

  /* Mobile Breakpoints */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1440px;

  /* Mobile-specific spacing */
  --mobile-spacing-xs: 2px;
  --mobile-spacing-sm: 4px;
  --mobile-spacing-md: 8px;
  --mobile-spacing-lg: 12px;
  --mobile-spacing-xl: 16px;

  /* Touch targets */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
}

/* Global box-sizing for easier layout calculations */
*, *::before, *::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh; /* Ensure body takes at least full viewport height */
  background-color: var(--color-background);
  color: var(--color-very-dark-gray);
}

#app {
  /* App container should not have default padding/margin if layout component handles it */
  max-width: none;
  margin: 0;
  padding: 0;
  text-align: left; /* Align content left by default */
  height: 100vh; /* Ensure app container takes full height */
}

h1, h2, h3, h4, h5, h6 {
  color: var(--color-very-dark-gray); /* Headings color */
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2.5em;
}

h2 {
  font-size: 2em;
}

h3 {
  font-size: 1.75em;
}

a {
  font-weight: 500;
  color: var(--color-primary);
  text-decoration: none; /* Remove default underline */
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline; /* Add underline on hover */
}

button {
  border-radius: 4px; /* Slightly less rounded */
  border: 1px solid transparent;
  padding: 0.8em 1.5em; /* More padding */
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--color-primary);
  color: var(--color-white);
  cursor: pointer;
  transition: background-color 0.25s ease, border-color 0.25s ease;
}

button:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark); /* Match border on hover */
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
  outline-color: var(--color-primary); /* Use primary color for focus */
}

input[type="text"],
input[type="password"],
input[type="email"],
textarea,
select {
  padding: 10px;
  border: 1px solid var(--color-mid-light-gray);
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 1em;
  color: var(--color-very-dark-gray);
  background-color: var(--color-white);
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25); /* Focus visible ring */
}

/* Add styles for focus indicator (WCAG 2.4.7) - override default if needed */
*:focus-visible {
    outline: 2px solid var(--color-accent); /* Use accent color for focus */
    outline-offset: 2px;
}

/* --- App Layout Styles (from previous step, add if not already present) --- */
/* Note: These styles assume #app is the container for the main layout */
/* If using a separate AppLayout component, apply these styles there */

/* Basic layout for the Surgery Scheduling Screen */
.surgery-scheduling-layout {
  display: flex;
  /* Use calc for height if there's a fixed header/footer */
  height: calc(100vh - var(--header-height) - var(--footer-height)); /* Example height calculation based on variables */
  overflow: hidden; /* Prevent outer scrollbars */
}

.main-schedule-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Manage scrolling within this area */
}

/* .pending-surgeries-panel styles are mostly in PendingSurgeriesList.vue */
/* .details-panel styles are mostly in SurgeryDetailsPanel.vue */

/* Add any other global styles or utility classes here */

/* Helper class for visually hiding elements for accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* ===== MOBILE RESPONSIVE UTILITIES ===== */

/* Mobile-first responsive design utilities */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

.tablet-only {
  display: none;
}

/* Touch-friendly button sizing */
.btn-touch {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--spacing-sm) var(--spacing-md);
}

/* Mobile-friendly form controls */
.form-control-mobile {
  min-height: var(--touch-target-comfortable);
  font-size: var(--font-size-base); /* Prevent zoom on iOS */
}

/* Mobile spacing utilities */
.mobile-p-xs { padding: var(--mobile-spacing-xs); }
.mobile-p-sm { padding: var(--mobile-spacing-sm); }
.mobile-p-md { padding: var(--mobile-spacing-md); }
.mobile-p-lg { padding: var(--mobile-spacing-lg); }
.mobile-p-xl { padding: var(--mobile-spacing-xl); }

.mobile-m-xs { margin: var(--mobile-spacing-xs); }
.mobile-m-sm { margin: var(--mobile-spacing-sm); }
.mobile-m-md { margin: var(--mobile-spacing-md); }
.mobile-m-lg { margin: var(--mobile-spacing-lg); }
.mobile-m-xl { margin: var(--mobile-spacing-xl); }

/* Mobile layout utilities */
.mobile-stack {
  flex-direction: column;
}

.mobile-full-width {
  width: 100%;
}

.mobile-text-center {
  text-align: center;
}

.mobile-hidden {
  display: none;
}

/* Responsive grid system */
.responsive-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

.responsive-flex {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.responsive-flex > * {
  flex: 1;
  min-width: 0;
}

/* ===== MEDIA QUERIES ===== */

/* Small devices (landscape phones, 480px and up) */
@media (min-width: 480px) {
  .mobile-only {
    display: none;
  }

  .tablet-only {
    display: block;
  }

  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .tablet-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }

  .mobile-hidden {
    display: block;
  }

  .mobile-stack {
    flex-direction: row;
  }

  .mobile-full-width {
    width: auto;
  }

  .mobile-text-center {
    text-align: left;
  }

  /* Reset mobile spacing on larger screens */
  .mobile-p-xs { padding: var(--spacing-xs); }
  .mobile-p-sm { padding: var(--spacing-sm); }
  .mobile-p-md { padding: var(--spacing-md); }
  .mobile-p-lg { padding: var(--spacing-lg); }
  .mobile-p-xl { padding: var(--spacing-xl); }

  .mobile-m-xs { margin: var(--spacing-xs); }
  .mobile-m-sm { margin: var(--spacing-sm); }
  .mobile-m-md { margin: var(--spacing-md); }
  .mobile-m-lg { margin: var(--spacing-lg); }
  .mobile-m-xl { margin: var(--spacing-xl); }

  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

<template>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useScheduleStore } from '@/stores/scheduleStore';
import { useNotificationStore } from '@/stores/notificationStore';
import { storeToRefs } from 'pinia';
import GanttChart from './GanttChart.vue';
import ToastNotification from './ToastNotification.vue';
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp.vue';
import OptimizationSuggestions from './OptimizationSuggestions.vue';
import keyboardShortcuts from '@/services/keyboardShortcuts';

<style scoped>
.scheduling-container {
  padding: var(--spacing-md);
  background-color: var(--color-background);
  color: var(--color-text);
  height: calc(100vh - 60px); /* Assuming header is 60px */
  display: flex;
  flex-direction: column;
}

h1 {
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.scheduling-layout {
  display: flex;
  flex-grow: 1;
  gap: var(--spacing-md);
  overflow: hidden; /* Prevent layout from exceeding container height */
}

.left-panel, .right-panel {
  width: 25%;
  background-color: var(--color-background-soft);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* Allow scrolling within panels */
}

.main-panel {
  flex-grow: 1;
  background-color: var(--color-background-soft);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Important for Gantt chart layout */
}

.left-panel h2, .main-panel h2, .right-panel h2 {
  color: var(--color-text);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-sm);
}

.filters-section, .sort-section {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.filters-section h3, .sort-section h3 {
  margin-top: 0;
  margin-bottom: 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

.filter-group {
  margin-bottom: var(--spacing-sm);
}

.filter-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.filter-group select,
.filter-group input[type="text"],
.filter-group input[type="date"] {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
  color: var(--color-text);
}

.advanced-filters {
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--color-background-soft);
  border-radius: var(--border-radius-sm);
  border-left: 3px solid var(--color-primary);
}

.date-range-inputs {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.date-range-separator {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.btn-link {
  background: none;
  border: none;
  color: var(--color-primary);
  text-decoration: underline;
  padding: 0;
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.btn-link:hover {
  color: var(--color-primary-dark, #0056b3);
  text-decoration: none;
}

.sort-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.sort-direction {
  display: flex;
  gap: var(--spacing-xs);
}

.sort-direction button {
  flex: 1;
}

.pending-surgeries-list {
  flex-grow: 1;
  overflow-y: auto;
}

.pending-surgeries-list ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.pending-surgery-item {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  cursor: grab;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pending-surgery-item:hover {
  background-color: var(--color-background-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.pending-surgery-item.selected {
  border-color: var(--color-primary);
  background-color: var(--color-background-active);
}

.pending-surgery-item.priority-high {
  border-left: 4px solid var(--color-error);
}

.pending-surgery-item.priority-medium {
  border-left: 4px solid var(--color-warning, #f59e0b);
}

.pending-surgery-item.priority-low {
  border-left: 4px solid var(--color-success, #10b981);
}

.pending-surgery-item.dragging {
  opacity: 0.4;
  transform: scale(1.02);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-style: dashed;
}

/* Drag ghost element */
.surgery-drag-ghost {
  display: flex;
  background-color: var(--color-background);
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 250px;
  pointer-events: none;
  z-index: 1000;
}

.ghost-priority {
  width: 8px;
  margin-right: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.ghost-priority.high {
  background-color: var(--color-error);
}

.ghost-priority.medium {
  background-color: var(--color-warning, #f59e0b);
}

.ghost-priority.low {
  background-color: var(--color-success, #10b981);
}

.ghost-content {
  flex: 1;
}

.ghost-title {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ghost-type {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.pending-surgery-item.dragging {
  opacity: 0.4;
  transform: scale(1.02);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-style: dashed;
}

/* Drag ghost element */
.surgery-drag-ghost {
  display: flex;
  background-color: var(--color-background);
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 250px;
  pointer-events: none;
  z-index: 1000;
}

.ghost-priority {
  width: 8px;
  margin-right: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.ghost-priority.high {
  background-color: var(--color-error);
}

.ghost-priority.medium {
  background-color: var(--color-warning, #f59e0b);
}

.ghost-priority.low {
  background-color: var(--color-success, #10b981);
}

.ghost-content {
  flex: 1;
}

.ghost-title {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ghost-type {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Drop target indicator */
.gantt-chart-container::after {
  content: attr(data-drop-message);
  display: none;
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--color-background);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
  font-size: var(--font-size-sm);
  pointer-events: none;
}

.gantt-chart-container.drag-over::after {
  display: block;
}

.gantt-chart-container.drag-over.invalid::after {
  background-color: var(--color-error-bg, rgba(255, 0, 0, 0.1));
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

.item-header {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.patient-info {
  display: flex;
  flex-direction: column;
}

.patient-name {
  font-size: var(--font-size-md);
  color: var(--color-text);
}

.patient-id {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-normal);
}

.priority-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: white;
}

.priority-badge.priority-high {
  background-color: var(--color-error);
}

.priority-badge.priority-medium {
  background-color: var(--color-warning, #f59e0b);
}

.priority-badge.priority-low {
  background-color: var(--color-success, #10b981);
}

.item-details {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--color-background-soft);
  border-radius: var(--border-radius-sm);
}

.surgery-type, .surgery-duration {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.surgery-full-type {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.label {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.value {
  font-weight: var(--font-weight-medium);
}

.item-status {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-sm);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

.status-indicator.status-pending {
  background-color: var(--color-warning, #f59e0b);
}

.status-indicator.status-scheduled {
  background-color: var(--color-primary);
}

.status-indicator.status-completed {
  background-color: var(--color-success, #10b981);
}

.status-indicator.status-cancelled {
  background-color: var(--color-error);
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.item-actions .icon {
  margin-right: var(--spacing-xs);
}

.no-items, .no-scheduled-items {
    padding: 10px;
    text-align: center;
    color: var(--text-color-secondary);
    font-style: italic;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.schedule-controls button {
  margin-left: 10px;
  padding: 8px 12px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.schedule-controls button:hover {
  background-color: var(--primary-color-dark);
}

.schedule-controls span {
    margin: 0 10px;
    font-weight: bold;
}

.gantt-chart-placeholder {
  flex-grow: 1;
  border: 2px dashed var(--border-color);
  display: flex;
  flex-direction: column; /* To stack p and ul */
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-color-secondary);
  border-radius: 4px;
  background-color: var(--background-color-light);
  min-height: 300px; /* Ensure it has some height */
  overflow-y: auto; /* If debug list gets long */
}

.gantt-chart-container {
  position: relative;
}

/* Drop target indicator */
.gantt-chart-container::after {
  content: attr(data-drop-message);
  display: none;
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--color-background);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
  font-size: var(--font-size-sm);
  pointer-events: none;
}

.gantt-chart-container.drag-over::after {
  display: block;
}

.gantt-chart-container.drag-over.invalid::after {
  background-color: var(--color-error-bg, rgba(255, 0, 0, 0.1));
  color: var(--color-error);
  border: 1px solid var(--color-error);
}

.scheduled-surgery-list-debug {
    list-style: none;
    padding: 0;
    margin-top: 10px;
    font-size: 0.9em;
}
.scheduled-surgery-list-debug li {
    padding: 5px;
    border-bottom: 1px solid var(--border-color-light);
    cursor: pointer;
}
.scheduled-surgery-list-debug li:hover {
    background-color: var(--hover-color);
}


.right-panel form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-md);
}

.form-group label {
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group .required {
  color: var(--color-error);
  margin-left: var(--spacing-xs);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="datetime-local"],
.form-group select,
.form-group textarea {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb, 0, 120, 212), 0.25);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background-color: var(--color-background-mute);
  color: var(--color-text-secondary);
  cursor: not-allowed;
}

.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea,
.form-group input.is-invalid,
.form-group select.is-invalid,
.form-group textarea.is-invalid {
  border-color: var(--color-error);
}

.form-error-message {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--spacing-xs);
}

.form-error-message.general-error {
  background-color: rgba(var(--color-error-rgb, 255, 0, 0), 0.1);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-md);
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

.form-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: background-color 0.2s ease;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark, #0056b3);
}

.btn-secondary {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

.btn-secondary:hover {
  background-color: var(--color-background-active);
}

/* Enhanced responsive design for mobile-first approach */

/* Tablet adjustments (768px - 1200px) */
@media (max-width: 1200px) {
  .scheduling-layout {
    flex-direction: column;
    overflow: visible;
    gap: var(--spacing-md);
  }

  .left-panel, .right-panel, .main-panel {
    width: 100%;
    margin-bottom: 0;
    max-height: 60vh;
    overflow-y: auto;
  }

  .main-panel {
    min-height: 400px;
    order: 1; /* Ensure main panel comes first on tablet */
  }

  .left-panel {
    order: 2;
    max-height: 40vh;
  }

  .right-panel {
    order: 3;
    max-height: 40vh;
  }
}

/* Mobile adjustments (up to 768px) */
@media (max-width: 768px) {
  .scheduling-screen {
    padding: var(--spacing-sm);
  }

  .scheduling-layout {
    gap: var(--spacing-sm);
  }

  .left-panel, .right-panel, .main-panel {
    max-height: none;
    min-height: auto;
  }

  .main-panel {
    min-height: 300px;
  }

  .left-panel, .right-panel {
    max-height: 300px;
  }

  /* Mobile-friendly form controls */
  .form-group input,
  .form-group select,
  .form-group textarea {
    min-height: var(--touch-target-comfortable);
    font-size: var(--font-size-base);
    padding: var(--spacing-sm) var(--spacing-md);
  }

  /* Mobile-friendly buttons */
  .btn {
    min-height: var(--touch-target-min);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }

  .btn-sm {
    min-height: 40px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  /* Mobile action buttons */
  .action-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .action-buttons .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Small mobile adjustments (up to 480px) */
@media (max-width: 480px) {
  .scheduling-screen {
    padding: var(--spacing-xs);
  }

  .main-panel {
    min-height: 250px;
  }

  .left-panel, .right-panel {
    max-height: 250px;
  }

  /* Compact form layout */
  .form-group {
    margin-bottom: var(--spacing-sm);
  }

  .form-group label {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
  }
}

/* Landscape orientation for mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .scheduling-layout {
    flex-direction: row;
    overflow-x: auto;
  }

  .left-panel, .right-panel {
    min-width: 250px;
    max-width: 300px;
    max-height: calc(100vh - var(--header-height) - 40px);
  }

  .main-panel {
    flex: 1;
    min-width: 400px;
    max-height: calc(100vh - var(--header-height) - 40px);
  }
}

</style>